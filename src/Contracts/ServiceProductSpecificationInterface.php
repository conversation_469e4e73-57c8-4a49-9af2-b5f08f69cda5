<?php

namespace Stephenchenorg\BaseFilamentPlugin\Contracts;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ProductSpecification;

interface ServiceProductSpecificationInterface
{
    public function getCombinationName(?string $combinationKey, string $lang = null): ?string;

    public function validateCombinationKey(string $combinationKey, Product $product): bool;

    public function getSellingPrice(ProductSpecification $productSpecification): ?float;

    public function getListingPrice(ProductSpecification $productSpecification): ?float;
}
