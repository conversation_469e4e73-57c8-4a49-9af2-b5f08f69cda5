<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Order;

use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceOrderInterface;
use Stephenchenorg\BaseFilamentPlugin\DTO\Order\OrderItemListDTO;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class ValidateCartQuery extends Query
{
    protected $attributes = [
        'name' => EnumNames::ValidateCart,
        'description' => 'Validates cart items against inventory and returns validation result',
    ];

    /**
     * @var ServiceOrderInterface
     */
    private ServiceOrderInterface $serviceOrder;

    /**
     * @param ServiceOrderInterface $serviceOrder
     */
    public function __construct(ServiceOrderInterface $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::CartValidationResult);
    }

    public function args(): array
    {
        return [
            'items' => [
                'name' => 'items',
                'type' => Type::listOf(GraphQL::type('OrderItemInput')),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {

        $itemListDTO = OrderItemListDTO::fromArray($args['items']);
        $this->serviceOrder->checkInventory($itemListDTO);

        return [
            'success' => true,
        ];
    }
}
