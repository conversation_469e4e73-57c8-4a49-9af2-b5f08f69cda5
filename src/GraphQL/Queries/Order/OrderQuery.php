<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Order;

use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceOrderInterface;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;

class OrderQuery extends Query
{
    use HasTranslation;

    protected $attributes = [
        'name' => EnumNames::Order,
        'description' => 'Get a single order by order key',
    ];

    /**
     * @var ServiceOrderInterface
     */
    public ServiceOrderInterface $serviceOrder;

    /**
     * @param ServiceOrderInterface $service
     */
    public function __construct(ServiceOrderInterface $service)
    {
        $this->serviceOrder = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Order);
    }

    public function args(): array
    {
        return [
            'order_key' => [
                'name' => 'order_key',
                'type' => Type::string(),
                'rules' => ['required', 'string'],
                'description' => 'The order key to search for',
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = Order::query()->visibleTo();

        return $query->where('order_key', $args['order_key'])->firstOrFail();
    }
}
