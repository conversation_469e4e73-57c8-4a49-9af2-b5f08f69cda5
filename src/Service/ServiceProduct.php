<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceProductInterface;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;

class ServiceProduct implements ServiceProductInterface
{

    public function getSellingPrice(Product $product): ?float
    {
        if ($product->specifications->count() == 0) {
            return null;
        }

        return $product->specifications->min('selling_price');
    }

    public function getListingPrice(Product $product): ?float
    {
        if ($product->specifications->count() == 0) {
            return null;
        }

        return $product->specifications->min('listing_price');
    }


}
